#!/usr/bin/env python
import os
import sys
import django

# Add the project directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ElDawliya_sys.settings')
django.setup()

print("Testing individual imports from Hr/urls.py...")

imports_to_test = [
    ("django.urls", "path, include"),
    ("Hr.views.employee_views", "dashboard, employee_list, employee_create, employee_detail, employee_edit, employee_delete, employee_search, employee_print, employee_detail_view, employee_dashboard_simple, employee_export, employee_list_ajax"),
    ("Hr.views.department_views_updated", "department_list, department_create, department_edit, department_delete, department_performance, department_detail"),
    ("Hr.views.job_views", "job_list, job_create, job_detail, job_edit, job_delete, get_next_job_code"),
    ("Hr.views.attendance_views", "attendance_rule_list, attendance_rule_create, attendance_rule_edit, attendance_rule_delete, employee_attendance_rule_list, employee_attendance_rule_create, employee_attendance_rule_edit, employee_attendance_rule_delete, employee_attendance_rule_bulk_create, official_holiday_list, official_holiday_create, official_holiday_edit, official_holiday_delete, attendance_machine_list, attendance_machine_create, attendance_machine_edit, attendance_machine_delete, attendance_record_list, attendance_record_create, attendance_record_edit, attendance_record_delete, fetch_attendance_data, attendance_summary_list, attendance_summary_detail"),
    ("Hr.views.salary_views", "salary_item_list, salary_item_create, salary_item_edit, salary_item_delete, employee_salary_item_list, employee_salary_item_create, employee_salary_item_bulk_create, employee_salary_item_edit, employee_salary_item_delete, payroll_calculate, payroll_period_create, payroll_period_edit, payroll_period_delete, payroll_entry_list, payroll_entry_detail, payroll_entry_approve, payroll_entry_reject, payroll_period_list"),
    ("Hr.views.report_views", "report_list, report_detail, monthly_salary_report, employee_report"),
    ("Hr.views.analytics_views", "analytics_dashboard, analytics_chart"),
    ("Hr.views.org_chart_views", "org_chart, org_chart_data, department_org_chart, employee_hierarchy"),
    ("Hr.views.alert_views", "alert_list"),
    ("Hr.views", "update_data"),
]

for module, items in imports_to_test:
    try:
        exec(f"from {module} import {items}")
        print(f"✓ {module}: {items[:50]}{'...' if len(items) > 50 else ''}")
    except Exception as e:
        print(f"✗ {module}: {e}")
        import traceback
        traceback.print_exc()
        break  # Stop at first error
