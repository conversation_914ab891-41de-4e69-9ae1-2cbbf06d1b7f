from django.urls import path, include

# Test imports one by one to find the problematic one
print("Testing imports...")

try:
    from .views.employee_views import (
        dashboard, employee_list, employee_create, employee_detail,
        employee_edit, employee_delete, employee_search, employee_print,
        employee_detail_view, employee_dashboard_simple, employee_export,
        employee_list_ajax
    )
    print("✓ employee_views imported successfully")
except Exception as e:
    print(f"✗ Error importing employee_views: {e}")

try:
    from .views.department_views_updated import (
        department_list, department_create, department_edit,
        department_delete, department_performance, department_detail
    )
    print("✓ department_views_updated imported successfully")
except Exception as e:
    print(f"✗ Error importing department_views_updated: {e}")

try:
    from .views.job_views import job_list, job_create, job_detail, job_edit, job_delete, get_next_job_code
    print("✓ job_views imported successfully")
except Exception as e:
    print(f"✗ Error importing job_views: {e}")

try:
    from .views.attendance_views import (
        attendance_rule_list, attendance_rule_create, attendance_rule_edit, attendance_rule_delete,
        employee_attendance_rule_list, employee_attendance_rule_create, employee_attendance_rule_edit,
        employee_attendance_rule_delete, employee_attendance_rule_bulk_create,
        official_holiday_list, official_holiday_create, official_holiday_edit, official_holiday_delete,
        attendance_machine_list, attendance_machine_create, attendance_machine_edit, attendance_machine_delete,
        attendance_record_list, attendance_record_create, attendance_record_edit, attendance_record_delete,
        fetch_attendance_data, attendance_summary_list, attendance_summary_detail
    )
    print("✓ attendance_views imported successfully")
except Exception as e:
    print(f"✗ Error importing attendance_views: {e}")

try:
    from .views.salary_views import (
        salary_item_list, salary_item_create, salary_item_edit, salary_item_delete,
        employee_salary_item_list, employee_salary_item_create, employee_salary_item_bulk_create,
        employee_salary_item_edit, employee_salary_item_delete,
        payroll_calculate, payroll_period_create,
        payroll_period_edit, payroll_period_delete,
        payroll_entry_list, payroll_entry_detail, payroll_entry_approve, payroll_entry_reject,
        payroll_period_list
    )
    print("✓ salary_views imported successfully")
except Exception as e:
    print(f"✗ Error importing salary_views: {e}")

try:
    from .views.analytics_views import (
        analytics_dashboard, analytics_chart
    )
    print("✓ analytics_views imported successfully")
except Exception as e:
    print(f"✗ Error importing analytics_views: {e}")

try:
    from .views.org_chart_views import (
        org_chart, org_chart_data, department_org_chart, employee_hierarchy
    )
    print("✓ org_chart_views imported successfully")
except Exception as e:
    print(f"✗ Error importing org_chart_views: {e}")

try:
    from .views.alert_views import alert_list
    print("✓ alert_views imported successfully")
except Exception as e:
    print(f"✗ Error importing alert_views: {e}")

try:
    from .views import update_data
    print("✓ views.update_data imported successfully")
except Exception as e:
    print(f"✗ Error importing views.update_data: {e}")

# Test the problematic import that might be causing circular dependency
try:
    from . import views
    print("✓ FULL views module imported successfully")
except Exception as e:
    print(f"✗ Error importing FULL views module: {e}")

app_name = 'Hr'

# Employee patterns
employee_patterns = [
    path('', employee_list, name='list'),
    path('ajax/', employee_list_ajax, name='list_ajax'),
    path('create/', employee_create, name='create'),
    path('<int:emp_id>/', employee_detail, name='detail'),
    path('<int:emp_id>/edit/', employee_edit, name='edit'),
    path('<int:emp_id>/delete/', employee_delete, name='delete'),
    path('<int:emp_id>/print/', employee_print, name='print'),
    path('search/', employee_search, name='employee_search'),
    path('export/', employee_export, name='export'),
]

# Department patterns
department_patterns = [
    path('', department_list, name='list'),
    path('create/', department_create, name='create'),
    path('<int:dept_code>/', department_detail, name='detail'),
    path('<int:dept_code>/edit/', department_edit, name='edit'),
    path('<int:dept_code>/delete/', department_delete, name='delete'),
    path('<int:dept_code>/performance/', department_performance, name='performance'),
]

# Job patterns
job_patterns = [
    path('', job_list, name='list'),
    path('create/', job_create, name='create'),
    path('get_next_job_code/', get_next_job_code, name='get_next_job_code'),
    path('<int:jop_code>/', job_detail, name='detail'),
    path('<int:jop_code>/edit/', job_edit, name='edit'),
    path('<int:jop_code>/delete/', job_delete, name='delete'),
]

urlpatterns = [
    # Dashboard
    path('dashboard/', dashboard, name='dashboard'),
    path('dashboard_simple/', employee_dashboard_simple, name='dashboard_simple'),

    # Include patterns
    path('employees/', include((employee_patterns, 'employees'))),
    path('employees/detail_view/', employee_detail_view, name='detail_view'),
    path('departments/', include((department_patterns, 'departments'))),
    path('jobs/', include((job_patterns, 'jobs'))),

    # Root redirect
    path('', employee_list, name='list'),
    path('update-data/', update_data, name='update_data'),
]
