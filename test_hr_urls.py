#!/usr/bin/env python
import os
import sys
import django

# Add the project directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ElDawliya_sys.settings')
django.setup()

print("Testing Hr.urls import...")

try:
    import Hr.urls_minimal as Hr_urls
    print("✓ Hr.urls_minimal imported successfully")
    
    # Check if app_name exists
    if hasattr(Hr_urls, 'app_name'):
        print(f"✓ app_name found: {Hr_urls.app_name}")
    else:
        print("✗ app_name not found")
        print("Available attributes:", [attr for attr in dir(Hr_urls) if not attr.startswith('_')])

    # Check if urlpatterns exists
    if hasattr(Hr_urls, 'urlpatterns'):
        print(f"✓ urlpatterns found with {len(Hr_urls.urlpatterns)} patterns")
    else:
        print("✗ urlpatterns not found")
    
    # Test URL resolution
    from django.urls import reverse
    try:
        url = reverse('Hr:dashboard')
        print(f"✓ Hr:dashboard resolves to: {url}")
    except Exception as e:
        print(f"✗ Error resolving Hr:dashboard: {e}")
        
        # Check available namespaces
        from django.urls import get_resolver
        resolver = get_resolver()
        print(f"Available namespaces: {list(resolver.namespace_dict.keys())}")
        
except Exception as e:
    print(f"✗ Error importing Hr.urls: {e}")
    import traceback
    traceback.print_exc()
